package com.bikeana.web;

import com.bikeana.listing.api.BikeListingResponseDto;
import com.bikeana.listing.api.BikeListingSummaryDto;
import com.bikeana.listing.api.CreateBikeListingRequestDto;
import com.bikeana.listing.domain.*;
import com.bikeana.security.JwtAuthenticationFilter;
import com.bikeana.security.JwtService;
import com.bikeana.user.api.SignupRequestDto;
import com.bikeana.user.api.UserProfileUpdateRequestDto;
import com.bikeana.user.api.UserResponseDto;
import com.bikeana.user.domain.User;
import com.bikeana.user.domain.UserService;
import io.quarkus.qute.Engine;
import io.quarkus.qute.TemplateInstance;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Main web controller for the Bikeana application.
 * Handles both page rendering (GET requests) and HTMX form submissions (POST requests).
 */
@Controller
public class WebController {

    private final UserService userService;
    private final BikeListingService bikeListingService;
    private final BikeListingRepository bikeListingRepository;
    private final JwtService jwtService;
    private final Engine quteEngine;

    public WebController(UserService userService, BikeListingService bikeListingService,
                        BikeListingRepository bikeListingRepository, JwtService jwtService, Engine quteEngine) {
        this.userService = userService;
        this.bikeListingService = bikeListingService;
        this.bikeListingRepository = bikeListingRepository;
        this.jwtService = jwtService;
        this.quteEngine = quteEngine;
    }

    /**
     * Adds the current year to all views for dynamic copyright notices.
     */
    @ModelAttribute("currentYear")
    public int currentYear() {
        return LocalDate.now().getYear();
    }

    /**
     * Home page - shows landing page or redirects to dashboard if authenticated.
     */
    @GetMapping("/")
    public String index(Authentication authentication, Model model) {
        boolean isAuthenticated = authentication != null && authentication.isAuthenticated();
        model.addAttribute("isAuthenticated", isAuthenticated);
        return "index";
    }

    /**
     * Login page - shows login form.
     */
    @GetMapping("/login")
    public String login(Authentication authentication, Model model) {
        boolean isAuthenticated = authentication != null && authentication.isAuthenticated();
        model.addAttribute("isAuthenticated", isAuthenticated);
        return "login";
    }

    /**
     * Signup page - shows registration form.
     */
    @GetMapping("/signup")
    public String signup(Authentication authentication, Model model) {
        boolean isAuthenticated = authentication != null && authentication.isAuthenticated();
        model.addAttribute("isAuthenticated", isAuthenticated);
        return "signup";
    }

    /**
     * Dashboard page - shows user dashboard (requires authentication).
     */
    @GetMapping("/dashboard")
    public String dashboard(Authentication authentication, Model model) {
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

        var user = userService.findUserById(principal.getUserId());

        // Get user's listings for dashboard stats
        List<BikeListingSummaryDto> userListings = bikeListingService.getListingsBySeller(principal.getUserId());
        long activeListingsCount = userListings.stream()
                .filter(listing -> ListingStatus.ACTIVE.equals(listing.status()))
                .count();

        model.addAttribute("user", user.orElse(null));
        model.addAttribute("userName", principal.getEmail());
        model.addAttribute("isAuthenticated", true);
        model.addAttribute("activeListingsCount", activeListingsCount);
        model.addAttribute("totalListingsCount", userListings.size());
        return "dashboard";
    }

    /**
     * Profile page - shows user profile management (requires authentication).
     */
    @GetMapping("/profile")
    public String profile(Authentication authentication, Model model) {
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

        var user = userService.findUserById(principal.getUserId());

        model.addAttribute("user", user.orElse(null));
        model.addAttribute("userName", principal.getEmail());
        model.addAttribute("isAuthenticated", true);
        return "profile";
    }

    /**
     * Listings page - shows all active bike listings with search and filters.
     */
    @GetMapping("/listings")
    public String listings(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String q,
            @RequestParam(required = false) BikeType bikeType,
            @RequestParam(required = false) BigDecimal minPrice,
            @RequestParam(required = false) BigDecimal maxPrice,
            @RequestParam(required = false) ConditionTier conditionTier,
            @RequestParam(required = false) FrameMaterial frameMaterial,
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String state,
            Authentication authentication,
            Model model) {

        boolean isAuthenticated = authentication != null && authentication.isAuthenticated();

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<BikeListingSummaryDto> listings = bikeListingService.searchListings(
                q, bikeType, minPrice, maxPrice, conditionTier, frameMaterial, city, state, pageable);

        model.addAttribute("listings", listings);
        model.addAttribute("isAuthenticated", isAuthenticated);
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", listings.getTotalPages());
        model.addAttribute("searchTerm", q);
        model.addAttribute("selectedBikeType", bikeType);
        model.addAttribute("minPrice", minPrice);
        model.addAttribute("maxPrice", maxPrice);
        model.addAttribute("selectedCondition", conditionTier);
        model.addAttribute("selectedFrameMaterial", frameMaterial);
        model.addAttribute("city", city);
        model.addAttribute("state", state);

        // Add enum values for filters
        model.addAttribute("bikeTypes", Arrays.asList(BikeType.values()));
        model.addAttribute("conditionTiers", Arrays.asList(ConditionTier.values()));
        model.addAttribute("frameMaterials", Arrays.asList(FrameMaterial.values()));

        return "listings";
    }

    /**
     * Individual listing page - shows detailed bike listing.
     */
    @GetMapping("/listings/{slug}")
    public String listingDetail(@PathVariable String slug, Authentication authentication, Model model) {
        boolean isAuthenticated = authentication != null && authentication.isAuthenticated();

        UUID requestingUserId = null;
        if (isAuthenticated) {
            JwtAuthenticationFilter.UserPrincipal principal =
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();
            requestingUserId = principal.getUserId();
        }

        Optional<BikeListingResponseDto> listing = bikeListingService.findListingBySlug(slug, requestingUserId);

        if (listing.isEmpty()) {
            return "error/404";
        }

        model.addAttribute("listing", listing.get());
        model.addAttribute("isAuthenticated", isAuthenticated);
        model.addAttribute("canEdit", isAuthenticated &&
                listing.get().seller().id().equals(requestingUserId));

        return "listing-detail";
    }

    /**
     * Create listing page - shows form to create new bike listing (requires authentication).
     */
    @GetMapping("/listings/create")
    public String createListing(Authentication authentication, Model model) {
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

        var user = userService.findUserById(principal.getUserId());

        // Check if user can create listings
        if (user.isEmpty() || (!user.get().role().equals("SHOP") && !user.get().role().equals("USER"))) {
            return "error/403";
        }

        model.addAttribute("user", user.get());
        model.addAttribute("isAuthenticated", true);

        // Add enum values for form options
        model.addAttribute("frameMaterials", Arrays.asList(FrameMaterial.values()));
        model.addAttribute("bikeTypes", Arrays.asList(BikeType.values()));
        model.addAttribute("wheelSizes", Arrays.asList(WheelSize.values()));
        model.addAttribute("brakeTypes", Arrays.asList(BrakeType.values()));
        model.addAttribute("conditionTiers", Arrays.asList(ConditionTier.values()));
        model.addAttribute("photoTypes", Arrays.asList(PhotoType.values()));

        return "create-listing";
    }

    /**
     * My listings page - shows user's own listings (requires authentication).
     */
    @GetMapping("/my-listings")
    public String myListings(Authentication authentication, Model model) {
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

        var user = userService.findUserById(principal.getUserId());
        List<BikeListingSummaryDto> listings = bikeListingService.getListingsBySeller(principal.getUserId());

        model.addAttribute("user", user.orElse(null));
        model.addAttribute("listings", listings);
        model.addAttribute("isAuthenticated", true);

        return "my-listings";
    }

    /**
     * Show photo capture interface for a listing.
     */
    @GetMapping("/listings/{listingId}/capture-photos")
    public String capturePhotos(@PathVariable UUID listingId,
                               Authentication authentication,
                               Model model) {
        if (authentication == null) {
            return "redirect:/login";
        }

        // Get user from authentication
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

        // Find the listing and verify ownership
        Optional<BikeListing> listingOpt = bikeListingRepository.findById(listingId);
        if (listingOpt.isEmpty()) {
            return "error/404";
        }

        BikeListing listing = listingOpt.get();
        if (!listing.getSeller().getId().equals(principal.getUserId())) {
            return "error/403";
        }

        model.addAttribute("listing", listing);
        model.addAttribute("listingId", listingId);
        model.addAttribute("isAuthenticated", true);
        return "photo-capture";
    }

    /**
     * Show traditional photo upload interface for a listing.
     */
    @GetMapping("/listings/{listingId}/upload-photos")
    public String uploadPhotos(@PathVariable UUID listingId,
                              Authentication authentication,
                              Model model) {
        if (authentication == null) {
            return "redirect:/login";
        }

        // Get user from authentication
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

        // Find the listing and verify ownership
        Optional<BikeListing> listingOpt = bikeListingRepository.findById(listingId);
        if (listingOpt.isEmpty()) {
            return "error/404";
        }

        BikeListing listing = listingOpt.get();
        if (!listing.getSeller().getId().equals(principal.getUserId())) {
            return "error/403";
        }

        model.addAttribute("listing", listing);
        model.addAttribute("listingId", listingId);
        model.addAttribute("isAuthenticated", true);
        return "upload-photos";
    }

    /**
     * Show photo capture demo page (no authentication required).
     */
    @GetMapping("/demo/photo-capture")
    public String photoCaptureDemo(Model model) {
        model.addAttribute("isAuthenticated", false);
        return "photo-capture-demo";
    }

    // ========================================
    // HTMX Form Submission Endpoints
    // ========================================

    /**
     * DTO for login requests in HTMX forms.
     */
    public static class LoginRequest {
        private String email;
        private String password;

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
    }

    /**
     * Handle user signup via HTMX.
     */
    @PostMapping("/htmx/auth/signup")
    public String signup(@Valid @ModelAttribute SignupRequestDto signupRequest,
                         BindingResult bindingResult,
                         HttpServletResponse response,
                         Model model) {
        if (bindingResult.hasErrors()) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Please fix the validation errors and try again.");
            model.addAttribute("errors", bindingResult.getAllErrors());
            return "alert";
        }

        try {
            UserResponseDto user = userService.registerUser(signupRequest);

            // Generate tokens
            String accessToken = jwtService.generateAccessToken(user.id(), user.email(), user.role());
            String refreshToken = jwtService.generateRefreshToken(user.id());

            // Set JWT token as HTTP-only cookie
            Cookie accessCookie = new Cookie("access_token", accessToken);
            accessCookie.setHttpOnly(true);
            accessCookie.setPath("/");
            accessCookie.setMaxAge(15 * 60); // 15 minutes
            response.addCookie(accessCookie);

            Cookie refreshCookie = new Cookie("refresh_token", refreshToken);
            refreshCookie.setHttpOnly(true);
            refreshCookie.setPath("/");
            refreshCookie.setMaxAge(7 * 24 * 60 * 60); // 7 days
            response.addCookie(refreshCookie);

            // Set HTMX redirect header
            response.setHeader("HX-Redirect", "/");

            model.addAttribute("type", "success");
            model.addAttribute("message", "Account created successfully! Redirecting to home page...");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";

        } catch (UserService.UserAlreadyExistsException e) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "An account with this email already exists.");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";
        } catch (Exception e) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Failed to create account. Please try again.");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";
        }
    }

    /**
     * Handle user login via HTMX.
     */
    @PostMapping("/htmx/auth/login")
    public String login(@Valid @ModelAttribute LoginRequest loginRequest,
                        BindingResult bindingResult,
                        HttpServletResponse response,
                        Model model) {
        if (bindingResult.hasErrors()) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Please enter valid email and password.");
            model.addAttribute("errors", bindingResult.getAllErrors());
            return "alert";
        }

        try {
            Optional<User> userOpt = userService.authenticateUser(loginRequest.getEmail(), loginRequest.getPassword());

            if (userOpt.isEmpty()) {
                model.addAttribute("type", "error");
                model.addAttribute("message", "Invalid email or password.");
                model.addAttribute("errors", java.util.Collections.emptyList());
                return "alert";
            }

            User user = userOpt.get();

            // Generate tokens
            String accessToken = jwtService.generateAccessToken(user.getId(), user.getEmail(), user.getRole());
            String refreshToken = jwtService.generateRefreshToken(user.getId());

            // Set JWT token as HTTP-only cookie
            Cookie accessCookie = new Cookie("access_token", accessToken);
            accessCookie.setHttpOnly(true);
            accessCookie.setPath("/");
            accessCookie.setMaxAge(15 * 60); // 15 minutes
            response.addCookie(accessCookie);

            Cookie refreshCookie = new Cookie("refresh_token", refreshToken);
            refreshCookie.setHttpOnly(true);
            refreshCookie.setPath("/");
            refreshCookie.setMaxAge(7 * 24 * 60 * 60); // 7 days
            response.addCookie(refreshCookie);

            // Set HTMX redirect header
            response.setHeader("HX-Redirect", "/");

            model.addAttribute("type", "success");
            model.addAttribute("message", "Login successful! Redirecting...");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";

        } catch (Exception e) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Login failed. Please try again.");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";
        }
    }

    /**
     * Handle user profile update via HTMX.
     */
    @PostMapping("/htmx/profile/update")
    public String updateProfile(@Valid @ModelAttribute UserProfileUpdateRequestDto profileRequest,
                               BindingResult bindingResult,
                               Authentication authentication,
                               Model model) {
        if (bindingResult.hasErrors()) {
            model.addAttribute("user", null);
            model.addAttribute("errors", bindingResult.getAllErrors());
            return "userEditForm";
        }

        try {
            JwtAuthenticationFilter.UserPrincipal principal =
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

            Optional<UserResponseDto> updatedUser = userService.updateUserProfile(
                    principal.getUserId(),
                    profileRequest.fullName(),
                    profileRequest.phone());

            // Return to view mode with updated user data
            var user = userService.findUserById(principal.getUserId());
            model.addAttribute("user", user.orElse(null));
            model.addAttribute("message", "Profile updated successfully!");
            model.addAttribute("isAuthenticated", true);
            return "userCard";

        } catch (Exception e) {
            model.addAttribute("user", null);
            model.addAttribute("errors", java.util.List.of("Failed to update profile. Please try again."));
            return "userEditForm";
        }
    }

    /**
     * Handle logout via HTMX.
     */
    @PostMapping("/htmx/auth/logout")
    public String logout(HttpServletResponse response, Model model) {
        // Clear JWT cookies
        Cookie accessCookie = new Cookie("access_token", "");
        accessCookie.setHttpOnly(true);
        accessCookie.setPath("/");
        accessCookie.setMaxAge(0);
        response.addCookie(accessCookie);

        Cookie refreshCookie = new Cookie("refresh_token", "");
        refreshCookie.setHttpOnly(true);
        refreshCookie.setPath("/");
        refreshCookie.setMaxAge(0);
        response.addCookie(refreshCookie);

        // Set HTMX redirect header
        response.setHeader("HX-Redirect", "/");

        model.addAttribute("type", "success");
        model.addAttribute("message", "Logged out successfully!");
        return "alert";
    }

    /**
     * Handle bike listing creation via HTMX.
     */
    @PostMapping("/htmx/listings/create")
    public String createListing(@Valid @ModelAttribute CreateBikeListingRequestDto listingRequest,
                               BindingResult bindingResult,
                               Authentication authentication,
                               HttpServletResponse response,
                               Model model) {
        if (bindingResult.hasErrors()) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Please fix the validation errors and try again.");
            model.addAttribute("errors", bindingResult.getAllErrors());
            return "alert";
        }

        try {
            JwtAuthenticationFilter.UserPrincipal principal =
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

            BikeListingResponseDto listing = bikeListingService.createListing(listingRequest, principal.getUserId());

            // Set HTMX redirect header to the photo capture page
            response.setHeader("HX-Redirect", "/listings/" + listing.id() + "/capture-photos");

            model.addAttribute("type", "success");
            model.addAttribute("message", "Listing created successfully! Now let's add photos...");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";

        } catch (Exception e) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Failed to create listing. Please try again.");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";
        }
    }

    /**
     * Handle listing status update via HTMX.
     */
    @PostMapping("/htmx/listings/{listingId}/status")
    public String updateListingStatus(@PathVariable UUID listingId,
                                     @RequestParam ListingStatus status,
                                     Authentication authentication,
                                     Model model) {
        try {
            JwtAuthenticationFilter.UserPrincipal principal =
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

            Optional<BikeListingResponseDto> listing = bikeListingService.updateListingStatus(
                    listingId, status, principal.getUserId());

            if (listing.isEmpty()) {
                model.addAttribute("type", "error");
                model.addAttribute("message", "Listing not found.");
                return "alert";
            }

            model.addAttribute("type", "success");
            model.addAttribute("message", "Listing status updated successfully!");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";

        } catch (Exception e) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Failed to update listing status. Please try again.");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";
        }
    }

    /**
     * Handle listing reservation via HTMX.
     */
    @PostMapping("/htmx/listings/{listingId}/reserve")
    public String reserveListing(@PathVariable UUID listingId,
                                @RequestParam(defaultValue = "24") int hours,
                                Authentication authentication,
                                Model model) {
        try {
            JwtAuthenticationFilter.UserPrincipal principal =
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

            Optional<BikeListingResponseDto> listing = bikeListingService.reserveListing(
                    listingId, principal.getUserId(), hours);

            if (listing.isEmpty()) {
                model.addAttribute("type", "error");
                model.addAttribute("message", "Listing not found.");
                return "alert";
            }

            model.addAttribute("type", "success");
            model.addAttribute("message", "Listing reserved successfully for " + hours + " hours!");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";

        } catch (Exception e) {
            model.addAttribute("type", "error");
            model.addAttribute("message", "Failed to reserve listing. Please try again.");
            model.addAttribute("errors", java.util.Collections.emptyList());
            return "alert";
        }
    }

    /**
     * Show profile edit form via HTMX.
     */
    @GetMapping("/htmx/users/profile/edit")
    public String showProfileEditForm(Authentication authentication, Model model) {
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

        var user = userService.findUserById(principal.getUserId());
        model.addAttribute("user", user.orElse(null));
        model.addAttribute("isAuthenticated", true);

        return "userEditForm";
    }

    /**
     * Show profile view (cancel edit) via HTMX.
     */
    @GetMapping("/htmx/users/profile/view")
    public String showProfileView(Authentication authentication, Model model) {
        JwtAuthenticationFilter.UserPrincipal principal =
            (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();

        var user = userService.findUserById(principal.getUserId());
        model.addAttribute("user", user.orElse(null));
        model.addAttribute("isAuthenticated", true);

        return "userCard";
    }
}
