package com.bikeana.listing.domain;

import com.bikeana.listing.api.*;
import com.bikeana.user.domain.User;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper class for converting between bike listing entities and DTOs.
 */
public class BikeListingMapper {

    /**
     * Converts CreateBikeListingRequestDto to BikeListing entity.
     */
    public static BikeListing toEntity(CreateBikeListingRequestDto dto, User seller, String seoSlug) {
        BikeListing listing = new BikeListing();
        
        // General Information
        listing.setTitle(dto.title());
        listing.setDescription(dto.description());
        listing.setStatus(ListingStatus.ACTIVE);
        
        // Bike Specifications
        listing.setBrand(dto.brand());
        listing.setModel(dto.model());
        listing.setYear(dto.year());
        listing.setFrameMaterial(dto.frameMaterial());
        listing.setFrameSize(dto.frameSize());
        listing.setBikeType(dto.bikeType());
        listing.setWheelSize(dto.wheelSize());
        listing.setDrivetrainSpeeds(dto.drivetrainSpeeds());
        listing.setGroupsetBrand(dto.groupsetBrand());
        listing.setBrakeType(dto.brakeType());
        listing.setColor(dto.color());
        
        // Condition & History
        listing.setConditionTier(dto.conditionTier());
        listing.setConditionNotes(dto.conditionNotes());
        listing.setServiceHistory(dto.serviceHistory());
        listing.setSerialNumber(dto.serialNumber());
        listing.setWarrantyInformation(dto.warrantyInformation());
        
        // Pricing & Sales Terms
        listing.setAskingPrice(dto.askingPrice());
        listing.setNegotiable(dto.negotiable());
        listing.setDepositRequired(dto.depositRequired());
        
        // Categorization & Search
        listing.setTags(dto.tags());
        listing.setSeoSlug(seoSlug);
        
        // Seller
        listing.setSeller(seller);
        
        // Availability
        listing.setAvailabilityStatus(AvailabilityStatus.AVAILABLE);
        
        return listing;
    }

    /**
     * Converts CreateBikeListingLocationDto to BikeListingLocation entity.
     */
    public static BikeListingLocation toLocationEntity(CreateBikeListingRequestDto.CreateBikeListingLocationDto dto, BikeListing listing) {
        BikeListingLocation location = new BikeListingLocation();
        
        location.setListing(listing);
        location.setStreetAddress(dto.streetAddress());
        location.setAddressLine2(dto.addressLine2());
        location.setCity(dto.city());
        location.setState(dto.state());
        location.setZipCode(dto.zipCode());
        location.setCountry("United States");
        
        location.setPublicNeighborhood(dto.publicNeighborhood());
        location.setPublicCity(dto.city());
        location.setPublicState(dto.state());
        
        location.setPickupInstructions(dto.pickupInstructions());
        location.setIsDefaultSellerAddress(dto.isDefaultSellerAddress() != null ? dto.isDefaultSellerAddress() : true);
        
        return location;
    }

    /**
     * Converts BikeListing entity to BikeListingResponseDto.
     * Includes privacy-aware location information.
     */
    public static BikeListingResponseDto toResponseDto(BikeListing listing, boolean showFullAddress, boolean showSellerContact) {
        return new BikeListingResponseDto(
                listing.getId(),
                listing.getTitle(),
                listing.getDescription(),
                listing.getStatus(),
                
                // Bike Specifications
                listing.getBrand(),
                listing.getModel(),
                listing.getYear(),
                listing.getFrameMaterial(),
                listing.getFrameSize(),
                listing.getBikeType(),
                listing.getWheelSize(),
                listing.getDrivetrainSpeeds(),
                listing.getGroupsetBrand(),
                listing.getBrakeType(),
                listing.getColor(),
                
                // Condition & History
                listing.getConditionTier(),
                listing.getConditionNotes(),
                listing.getServiceHistory(),
                listing.getSerialNumber(),
                listing.getWarrantyInformation(),
                
                // Pricing & Sales Terms
                listing.getAskingPrice(),
                listing.getNegotiable(),
                listing.getDepositRequired(),
                
                // Categorization & Search
                listing.getTags(),
                listing.getSeoSlug(),
                
                // Seller Information
                toSellerDto(listing.getSeller(), showSellerContact),
                
                // Photos
                listing.getPhotos().stream()
                        .map(BikeListingMapper::toPhotoDto)
                        .collect(Collectors.toList()),
                
                // Primary Photo
                listing.getPhotos().stream()
                        .filter(BikeListingPhoto::getIsPrimary)
                        .findFirst()
                        .map(BikeListingMapper::toPhotoDto)
                        .orElse(null),
                
                // Location
                listing.getLocation() != null ? toLocationDto(listing.getLocation(), showFullAddress) : null,
                
                // Availability
                listing.getAvailabilityStatus(),
                listing.getReservedByUserId(),
                listing.getReservationExpiresAt(),
                
                // Metadata
                listing.getCreatedAt(),
                listing.getUpdatedAt()
        );
    }

    /**
     * Converts BikeListing entity to BikeListingSummaryDto for listing grids.
     */
    public static BikeListingSummaryDto toSummaryDto(BikeListing listing) {
        return new BikeListingSummaryDto(
                listing.getId(),
                listing.getTitle(),
                listing.getBrand(),
                listing.getModel(),
                listing.getYear(),
                listing.getBikeType(),
                listing.getConditionTier(),
                listing.getAskingPrice(),
                listing.getNegotiable(),
                listing.getSeoSlug(),

                // Status
                listing.getStatus(),

                // Seller Summary
                new BikeListingSummaryDto.BikeListingSellerSummaryDto(
                        listing.getSeller().getId(),
                        listing.getSeller().getFullName(),
                        listing.getSeller().getRole()
                ),
                
                // Primary Photo Summary
                listing.getPhotos().stream()
                        .filter(BikeListingPhoto::getIsPrimary)
                        .findFirst()
                        .map(photo -> new BikeListingSummaryDto.BikeListingPhotoSummaryDto(
                                photo.getId(),
                                photo.getFileName(),
                                photo.getFilePath(),
                                photo.getAltText()
                        ))
                        .orElse(null),
                
                // Public Location
                listing.getLocation() != null ? listing.getLocation().getPublicDisplayAddress() : null,
                
                // Availability
                listing.getAvailabilityStatus(),
                
                // Metadata
                listing.getCreatedAt()
        );
    }

    /**
     * Converts User entity to BikeListingSellerDto.
     */
    private static BikeListingResponseDto.BikeListingSellerDto toSellerDto(User seller, boolean showContact) {
        return new BikeListingResponseDto.BikeListingSellerDto(
                seller.getId(),
                seller.getFullName(),
                seller.getRole(),
                showContact ? seller.getEmail() : null,
                showContact ? seller.getPhone() : null
        );
    }

    /**
     * Converts BikeListingPhoto entity to BikeListingPhotoDto.
     */
    private static BikeListingResponseDto.BikeListingPhotoDto toPhotoDto(BikeListingPhoto photo) {
        return new BikeListingResponseDto.BikeListingPhotoDto(
                photo.getId(),
                photo.getFileName(),
                photo.getFilePath(),
                photo.getContentType(),
                photo.getFileSize(),
                photo.getIsPrimary(),
                photo.getPhotoType(),
                photo.getAltText(),
                photo.getDisplayOrder(),
                photo.getCreatedAt()
        );
    }

    /**
     * Converts BikeListingLocation entity to BikeListingLocationDto.
     */
    private static BikeListingResponseDto.BikeListingLocationDto toLocationDto(BikeListingLocation location, boolean showFullAddress) {
        return new BikeListingResponseDto.BikeListingLocationDto(
                location.getId(),
                
                // Full address (only if authorized)
                showFullAddress ? location.getStreetAddress() : null,
                showFullAddress ? location.getAddressLine2() : null,
                showFullAddress ? location.getCity() : null,
                showFullAddress ? location.getState() : null,
                showFullAddress ? location.getZipCode() : null,
                showFullAddress ? location.getCountry() : null,
                
                // Public information (always shown)
                location.getPublicNeighborhood(),
                location.getPublicCity(),
                location.getPublicState(),
                location.getPublicDisplayAddress(),
                
                // Additional info
                showFullAddress ? location.getPickupInstructions() : null,
                location.getIsDefaultSellerAddress(),
                
                location.getCreatedAt(),
                location.getUpdatedAt()
        );
    }
}
