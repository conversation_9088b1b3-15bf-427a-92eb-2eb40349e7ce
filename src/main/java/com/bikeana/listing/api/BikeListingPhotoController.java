package com.bikeana.listing.api;

import com.bikeana.listing.domain.*;
import com.bikeana.security.JwtAuthenticationFilter;
import com.bikeana.user.api.ErrorResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * REST controller for bike listing photo operations.
 * Handles photo uploads, management, and primary photo designation.
 */
@RestController
@RequestMapping("/api/listings/{listingId}/photos")
public class BikeListingPhotoController {

    private final BikeListingRepository bikeListingRepository;
    private final PhotoService photoService;

    public BikeListingPhotoController(BikeListingRepository bikeListingRepository, PhotoService photoService) {
        this.bikeListingRepository = bikeListingRepository;
        this.photoService = photoService;
    }

    /**
     * Uploads photos for a bike listing.
     */
    @PostMapping
    @Transactional
    public ResponseEntity<?> uploadPhotos(
            @PathVariable UUID listingId,
            @RequestParam("files") List<MultipartFile> files,
            @RequestParam(value = "photoTypes", required = false) List<PhotoType> photoTypes,
            @RequestParam(value = "primaryPhotoIndex", defaultValue = "0") int primaryPhotoIndex,
            Authentication authentication) {
        
        try {
            JwtAuthenticationFilter.UserPrincipal principal = 
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();
            
            // Find the listing and verify ownership
            Optional<BikeListing> listingOpt = bikeListingRepository.findById(listingId);
            if (listingOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            BikeListing listing = listingOpt.get();
            if (!listing.getSeller().getId().equals(principal.getUserId())) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new ErrorResponse("FORBIDDEN", "You can only upload photos to your own listings"));
            }
            
            // Validate photo requirements
            if (files.size() < 5) {
                return ResponseEntity.badRequest()
                        .body(new ErrorResponse("INSUFFICIENT_PHOTOS", "Minimum 5 photos required"));
            }
            
            if (files.size() > 10) {
                return ResponseEntity.badRequest()
                        .body(new ErrorResponse("TOO_MANY_PHOTOS", "Maximum 10 photos allowed"));
            }
            
            if (primaryPhotoIndex >= files.size()) {
                return ResponseEntity.badRequest()
                        .body(new ErrorResponse("INVALID_PRIMARY_INDEX", "Primary photo index is out of range"));
            }
            
            // Clear existing photos
            listing.getPhotos().clear();
            
            // Upload and save photos
            for (int i = 0; i < files.size(); i++) {
                MultipartFile file = files.get(i);
                PhotoType photoType = (photoTypes != null && i < photoTypes.size()) ? 
                        photoTypes.get(i) : PhotoType.GENERAL;
                
                PhotoService.PhotoUploadResult uploadResult = photoService.uploadPhoto(file, listingId, photoType);
                
                BikeListingPhoto photo = new BikeListingPhoto(
                        listing,
                        uploadResult.getFileName(),
                        uploadResult.getRelativePath(),
                        uploadResult.getContentType(),
                        uploadResult.getFileSize(),
                        uploadResult.getPhotoType()
                );
                
                photo.setIsPrimary(i == primaryPhotoIndex);
                photo.setDisplayOrder(i);
                photo.setAltText(generateAltText(listing, photoType));
                
                listing.getPhotos().add(photo);
            }
            
            bikeListingRepository.save(listing);
            
            return ResponseEntity.ok(new PhotoUploadResponse(
                    "Photos uploaded successfully",
                    listing.getPhotos().size(),
                    listing.getPhotos().stream()
                            .filter(BikeListingPhoto::getIsPrimary)
                            .findFirst()
                            .map(photo -> photo.getFilePath())
                            .orElse(null),
                    listing.getSeoSlug()
            ));
            
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("INVALID_REQUEST", e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new ErrorResponse("UPLOAD_FAILED", "Failed to upload photos"));
        }
    }

    /**
     * Sets a photo as the primary photo for the listing.
     */
    @PatchMapping("/{photoId}/primary")
    @Transactional
    public ResponseEntity<?> setPrimaryPhoto(
            @PathVariable UUID listingId,
            @PathVariable UUID photoId,
            Authentication authentication) {
        
        try {
            JwtAuthenticationFilter.UserPrincipal principal = 
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();
            
            Optional<BikeListing> listingOpt = bikeListingRepository.findById(listingId);
            if (listingOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            BikeListing listing = listingOpt.get();
            if (!listing.getSeller().getId().equals(principal.getUserId())) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new ErrorResponse("FORBIDDEN", "You can only modify your own listings"));
            }
            
            // Find the photo and set as primary
            Optional<BikeListingPhoto> targetPhotoOpt = listing.getPhotos().stream()
                    .filter(photo -> photo.getId().equals(photoId))
                    .findFirst();
            
            if (targetPhotoOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            // Clear existing primary photo
            listing.getPhotos().forEach(photo -> photo.setIsPrimary(false));
            
            // Set new primary photo
            targetPhotoOpt.get().setIsPrimary(true);
            
            bikeListingRepository.save(listing);
            
            return ResponseEntity.ok(new PrimaryPhotoResponse("Primary photo updated successfully"));
            
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new ErrorResponse("UPDATE_FAILED", "Failed to update primary photo"));
        }
    }

    /**
     * Deletes a photo from the listing.
     */
    @DeleteMapping("/{photoId}")
    @Transactional
    public ResponseEntity<?> deletePhoto(
            @PathVariable UUID listingId,
            @PathVariable UUID photoId,
            Authentication authentication) {
        
        try {
            JwtAuthenticationFilter.UserPrincipal principal = 
                (JwtAuthenticationFilter.UserPrincipal) authentication.getPrincipal();
            
            Optional<BikeListing> listingOpt = bikeListingRepository.findById(listingId);
            if (listingOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            BikeListing listing = listingOpt.get();
            if (!listing.getSeller().getId().equals(principal.getUserId())) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(new ErrorResponse("FORBIDDEN", "You can only modify your own listings"));
            }
            
            // Find and remove the photo
            Optional<BikeListingPhoto> photoToRemove = listing.getPhotos().stream()
                    .filter(photo -> photo.getId().equals(photoId))
                    .findFirst();
            
            if (photoToRemove.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            
            BikeListingPhoto photo = photoToRemove.get();
            
            // Check if this would leave less than 5 photos
            if (listing.getPhotos().size() <= 5) {
                return ResponseEntity.badRequest()
                        .body(new ErrorResponse("MINIMUM_PHOTOS", "Cannot delete photo. Minimum 5 photos required"));
            }
            
            // Delete file from filesystem
            photoService.deletePhoto(photo.getFilePath());
            
            // Remove from listing
            listing.getPhotos().remove(photo);
            
            // If this was the primary photo, set another as primary
            if (photo.getIsPrimary() && !listing.getPhotos().isEmpty()) {
                listing.getPhotos().get(0).setIsPrimary(true);
            }
            
            bikeListingRepository.save(listing);
            
            return ResponseEntity.ok(new PhotoDeleteResponse("Photo deleted successfully"));
            
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new ErrorResponse("DELETE_FAILED", "Failed to delete photo"));
        }
    }

    /**
     * Generates alt text for photos based on listing and photo type.
     */
    private String generateAltText(BikeListing listing, PhotoType photoType) {
        String baseText = listing.getYear() + " " + listing.getBrand() + " " + listing.getModel();
        if (photoType != null) {
            return baseText + " - " + photoType.getDisplayName();
        }
        return baseText;
    }

    // Response DTOs
    public record PhotoUploadResponse(String message, int photoCount, String primaryPhotoPath, String seoSlug) {}
    public record PrimaryPhotoResponse(String message) {}
    public record PhotoDeleteResponse(String message) {}
}
