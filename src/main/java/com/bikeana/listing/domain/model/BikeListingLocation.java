package com.bikeana.listing.domain;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Entity representing the pickup location for a bike listing.
 * Manages address privacy by showing only general area until escrow activation.
 */
@Entity
@Table(name = "bike_listing_locations")
public class BikeListingLocation {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "listing_id", nullable = false)
    private BikeListing listing;

    // Full address (private until escrow)
    @Column(nullable = false, length = 200)
    private String streetAddress;

    @Column(length = 100)
    private String addressLine2;

    @Column(nullable = false, length = 100)
    private String city;

    @Column(nullable = false, length = 50)
    private String state;

    @Column(nullable = false, length = 20)
    private String zipCode;

    @Column(nullable = false, length = 100)
    private String country = "United States";

    // Public area information (shown before escrow)
    @Column(nullable = false, length = 100)
    private String publicNeighborhood;

    @Column(nullable = false, length = 100)
    private String publicCity;

    @Column(nullable = false, length = 50)
    private String publicState;

    // Geocoding information
    @Column(precision = 10, scale = 8)
    private BigDecimal latitude;

    @Column(precision = 11, scale = 8)
    private BigDecimal longitude;

    // Privacy settings
    @Column(nullable = false)
    private Boolean isDefaultSellerAddress = true;

    @Column(columnDefinition = "TEXT")
    private String pickupInstructions;

    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;

    // Constructors
    public BikeListingLocation() {}

    // Getters and Setters
    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public BikeListing getListing() {
        return listing;
    }

    public void setListing(BikeListing listing) {
        this.listing = listing;
    }

    public String getStreetAddress() {
        return streetAddress;
    }

    public void setStreetAddress(String streetAddress) {
        this.streetAddress = streetAddress;
    }

    public String getAddressLine2() {
        return addressLine2;
    }

    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getPublicNeighborhood() {
        return publicNeighborhood;
    }

    public void setPublicNeighborhood(String publicNeighborhood) {
        this.publicNeighborhood = publicNeighborhood;
    }

    public String getPublicCity() {
        return publicCity;
    }

    public void setPublicCity(String publicCity) {
        this.publicCity = publicCity;
    }

    public String getPublicState() {
        return publicState;
    }

    public void setPublicState(String publicState) {
        this.publicState = publicState;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public Boolean getIsDefaultSellerAddress() {
        return isDefaultSellerAddress;
    }

    public void setIsDefaultSellerAddress(Boolean isDefaultSellerAddress) {
        this.isDefaultSellerAddress = isDefaultSellerAddress;
    }

    public String getPickupInstructions() {
        return pickupInstructions;
    }

    public void setPickupInstructions(String pickupInstructions) {
        this.pickupInstructions = pickupInstructions;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * Returns the public display address (neighborhood, city, state).
     * Used for showing general location before escrow activation.
     */
    public String getPublicDisplayAddress() {
        return String.format("%s, %s, %s", publicNeighborhood, publicCity, publicState);
    }

    /**
     * Returns the full address for pickup.
     * Should only be shown after escrow activation.
     */
    public String getFullAddress() {
        StringBuilder address = new StringBuilder(streetAddress);
        if (addressLine2 != null && !addressLine2.trim().isEmpty()) {
            address.append(", ").append(addressLine2);
        }
        address.append(", ").append(city)
               .append(", ").append(state)
               .append(" ").append(zipCode);
        return address.toString();
    }
}
