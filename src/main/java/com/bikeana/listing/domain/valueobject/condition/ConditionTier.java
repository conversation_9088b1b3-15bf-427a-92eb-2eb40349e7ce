package com.bikeana.listing.domain.valueobject.condition;

/**
 * Enumeration for bike condition tiers.
 */
public enum ConditionTier {
    NEW("New"),
    EXCELLENT("Excellent"),
    GOOD("Good"),
    FAIR("Fair"),
    NEEDS_TLC("Needs TLC");

    private final String displayName;

    ConditionTier(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
