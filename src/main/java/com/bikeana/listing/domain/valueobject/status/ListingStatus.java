package com.bikeana.listing.domain.valueobject.status;

/**
 * Enumeration for bike listing status.
 */
public enum ListingStatus {
    ACTIVE("Active"),
    SOLD("Sold"),
    PENDING("Pending Sale"),
    INACTIVE("Inactive"),
    DRAFT("Draft");

    private final String displayName;

    ListingStatus(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }
}
