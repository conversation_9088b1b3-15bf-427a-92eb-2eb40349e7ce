package com.bikeana.listing.domain;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * Service for handling photo uploads and management.
 * Initially uses local filesystem storage with future migration to cloud storage.
 */
@Service
public class PhotoService {

    private final String uploadDir;
    private final String baseUrl;
    private final List<String> allowedContentTypes = Arrays.asList(
            "image/jpeg", "image/jpg", "image/png", "image/webp"
    );
    private final long maxFileSize = 10 * 1024 * 1024; // 10MB

    public PhotoService(
            @Value("${app.upload.directory:uploads/listings}") String uploadDir,
            @Value("${app.base-url:http://localhost:8080}") String baseUrl) {
        this.uploadDir = uploadDir;
        this.baseUrl = baseUrl;
        createUploadDirectoryIfNotExists();
    }

    /**
     * Uploads a photo file and returns the file information.
     */
    public PhotoUploadResult uploadPhoto(MultipartFile file, UUID listingId, PhotoType photoType) throws IOException {
        validateFile(file);
        
        String originalFilename = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);
        String fileName = generateUniqueFileName(listingId, photoType, fileExtension);
        
        Path listingDir = createListingDirectory(listingId);
        Path filePath = listingDir.resolve(fileName);
        
        // Copy file to destination
        Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
        
        String relativePath = getRelativePath(listingId, fileName);
        String publicUrl = baseUrl + "/uploads/" + relativePath;
        
        return new PhotoUploadResult(
                fileName,
                relativePath,
                publicUrl,
                file.getContentType(),
                file.getSize(),
                photoType
        );
    }

    /**
     * Deletes a photo file from the filesystem.
     */
    public boolean deletePhoto(String relativePath) {
        try {
            Path fullPath = Paths.get(uploadDir, relativePath);
            return Files.deleteIfExists(fullPath);
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * Validates uploaded file.
     */
    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("File is empty");
        }
        
        if (file.getSize() > maxFileSize) {
            throw new IllegalArgumentException("File size exceeds maximum allowed size of 10MB");
        }
        
        String contentType = file.getContentType();
        if (contentType == null || !allowedContentTypes.contains(contentType.toLowerCase())) {
            throw new IllegalArgumentException("File type not allowed. Allowed types: JPEG, PNG, WebP");
        }
    }

    /**
     * Gets file extension from filename.
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf('.') == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf('.'));
    }

    /**
     * Generates a unique filename for the photo.
     */
    private String generateUniqueFileName(UUID listingId, PhotoType photoType, String extension) {
        String photoTypePrefix = photoType != null ? photoType.name().toLowerCase() + "_" : "";
        return photoTypePrefix + UUID.randomUUID().toString() + extension;
    }

    /**
     * Creates the upload directory if it doesn't exist.
     */
    private void createUploadDirectoryIfNotExists() {
        try {
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
        } catch (IOException e) {
            throw new RuntimeException("Could not create upload directory", e);
        }
    }

    /**
     * Creates a directory for the specific listing.
     */
    private Path createListingDirectory(UUID listingId) throws IOException {
        Path listingDir = Paths.get(uploadDir, listingId.toString());
        if (!Files.exists(listingDir)) {
            Files.createDirectories(listingDir);
        }
        return listingDir;
    }

    /**
     * Gets the relative path for storing in database.
     */
    private String getRelativePath(UUID listingId, String fileName) {
        return listingId.toString() + "/" + fileName;
    }

    /**
     * Result object for photo upload operations.
     */
    public static class PhotoUploadResult {
        private final String fileName;
        private final String relativePath;
        private final String publicUrl;
        private final String contentType;
        private final Long fileSize;
        private final PhotoType photoType;

        public PhotoUploadResult(String fileName, String relativePath, String publicUrl, 
                               String contentType, Long fileSize, PhotoType photoType) {
            this.fileName = fileName;
            this.relativePath = relativePath;
            this.publicUrl = publicUrl;
            this.contentType = contentType;
            this.fileSize = fileSize;
            this.photoType = photoType;
        }

        public String getFileName() { return fileName; }
        public String getRelativePath() { return relativePath; }
        public String getPublicUrl() { return publicUrl; }
        public String getContentType() { return contentType; }
        public Long getFileSize() { return fileSize; }
        public PhotoType getPhotoType() { return photoType; }
    }
}
